{"rules": {"typescript": "warn", "correctness": "warn", "suspicious": "warn", "perf": "warn", "style": "warn", "pedantic": "off", "nursery": "off", "restriction": "off"}, "env": {"browser": false, "node": true, "es2022": true}, "globals": {"jest": "readonly", "describe": "readonly", "it": "readonly", "expect": "readonly", "beforeEach": "readonly", "afterEach": "readonly", "beforeAll": "readonly", "afterAll": "readonly"}, "ignore_patterns": ["node_modules/**", "dist/**", "coverage/**", "*.d.ts"]}